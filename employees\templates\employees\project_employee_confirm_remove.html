{% extends 'base.html' %}

{% block title %}إزالة الموظف من المشروع - Employee Management System{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg shadow-2xl rounded-2xl border border-gray-200/50 dark:border-gray-700/50 hover-lift">
        <div class="px-8 py-6 border-b border-gray-200/50 dark:border-gray-700/50 bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-900/50 dark:to-orange-800/50 rounded-t-2xl">
            <div class="flex items-center space-x-4">
                <div class="relative">
                    <i class="fas fa-user-minus text-3xl text-orange-600 dark:text-orange-400 float"></i>
                    <div class="absolute -inset-2 bg-orange-600/20 rounded-full blur opacity-50"></div>
                </div>
                <div>
                    <h1 class="text-3xl font-bold bg-gradient-to-r from-orange-600 to-orange-800 dark:from-orange-400 dark:to-orange-600 bg-clip-text text-transparent">
                        تأكيد إزالة الموظف
                    </h1>
                    <p class="text-sm text-orange-600 dark:text-orange-400 mt-1">إزالة الموظف من المشروع</p>
                </div>
            </div>
        </div>

        <div class="p-8">
            <div class="mb-8">
                <div class="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-xl p-6">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-orange-400 text-xl"></i>
                        </div>
                        <div class="mr-3">
                            <h3 class="text-lg font-medium text-orange-800 dark:text-orange-200 mb-2">
                                هل أنت متأكد من إزالة هذا الموظف من المشروع؟
                            </h3>
                            <p class="text-orange-700 dark:text-orange-300 mb-4">
                                سيتم إزالة الموظف "{{ project_employee.employee.full_name }}" من المشروع "{{ project.name }}". 
                                لن يتم حذف الموظف من النظام، بل سيتم إلغاء تعيينه في هذا المشروع فقط.
                            </p>
                            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-orange-200 dark:border-orange-700">
                                <h4 class="font-semibold text-gray-900 dark:text-white mb-2">ما سيحدث:</h4>
                                <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                    <li><i class="fas fa-check text-orange-500 ml-2"></i>سيتم إلغاء تعيين الموظف في هذا المشروع</li>
                                    <li><i class="fas fa-check text-orange-500 ml-2"></i>سيتم تسجيل تاريخ الإزالة</li>
                                    <li><i class="fas fa-times text-green-500 ml-2"></i>لن يتم حذف الموظف من النظام</li>
                                    <li><i class="fas fa-times text-green-500 ml-2"></i>يمكن إعادة تعيين الموظف لاحقاً</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Project and Employee Details -->
            <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 mb-8">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">تفاصيل الإزالة:</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Project Info -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                        <h4 class="font-semibold text-gray-900 dark:text-white mb-3">
                            <i class="fas fa-project-diagram ml-2 text-primary-600 dark:text-primary-400"></i>
                            معلومات المشروع
                        </h4>
                        <div class="space-y-2">
                            <div>
                                <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">اسم المشروع</label>
                                <p class="text-gray-900 dark:text-white">{{ project.name }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">حالة المشروع</label>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    {% if project.status == 'completed' %}bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                    {% elif project.status == 'in_progress' %}bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                    {% elif project.status == 'planning' %}bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                                    {% elif project.status == 'on_hold' %}bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200
                                    {% else %}bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200{% endif %}">
                                    {{ project.get_status_display }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Employee Info -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                        <h4 class="font-semibold text-gray-900 dark:text-white mb-3">
                            <i class="fas fa-user ml-2 text-primary-600 dark:text-primary-400"></i>
                            معلومات الموظف
                        </h4>
                        <div class="space-y-2">
                            <div>
                                <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">اسم الموظف</label>
                                <p class="text-gray-900 dark:text-white">{{ project_employee.employee.full_name }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">المنصب</label>
                                <p class="text-gray-900 dark:text-white">{{ project_employee.employee.position }}</p>
                            </div>
                            {% if project_employee.role %}
                            <div>
                                <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">الدور في المشروع</label>
                                <p class="text-gray-900 dark:text-white">{{ project_employee.role }}</p>
                            </div>
                            {% endif %}
                            <div>
                                <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">تاريخ التعيين</label>
                                <p class="text-gray-900 dark:text-white">{{ project_employee.assigned_date|date:"Y/m/d" }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <form method="post" class="flex justify-center space-x-4 space-x-reverse">
                {% csrf_token %}
                <button type="submit" class="bg-gradient-to-r from-orange-600 to-orange-700 hover:from-orange-700 hover:to-orange-800 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 btn-glow">
                    <i class="fas fa-user-minus ml-2"></i>نعم، أزل الموظف
                </button>
                <a href="{% url 'employees:project_detail' project.pk %}" class="bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
                    <i class="fas fa-times ml-2"></i>إلغاء
                </a>
            </form>
        </div>
    </div>
</div>
{% endblock %}
