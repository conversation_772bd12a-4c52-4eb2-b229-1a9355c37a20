{% extends 'base.html' %}

{% block title %}{{ title }} - Employee Management System{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg shadow-2xl rounded-2xl border border-gray-200/50 dark:border-gray-700/50 hover-lift">
        <div class="px-8 py-6 border-b border-gray-200/50 dark:border-gray-700/50 bg-gradient-to-r from-primary-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 rounded-t-2xl">
            <div class="flex items-center space-x-4">
                <div class="relative">
                    <i class="fas fa-project-diagram text-3xl text-primary-600 dark:text-primary-400 float"></i>
                    <div class="absolute -inset-2 bg-primary-600/20 rounded-full blur opacity-50"></div>
                </div>
                <div>
                    <h1 class="text-3xl font-bold bg-gradient-to-r from-primary-600 to-primary-800 dark:from-primary-400 dark:to-primary-600 bg-clip-text text-transparent">
                        {{ title }}
                    </h1>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">املأ معلومات المشروع أدناه</p>
                </div>
            </div>
        </div>

        <form method="post" enctype="multipart/form-data" class="p-8">
            {% csrf_token %}

            {% if form.non_field_errors %}
                <div class="mb-6 p-6 bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/50 dark:to-red-800/50 border border-red-200 dark:border-red-700 text-red-800 dark:text-red-200 rounded-xl shadow-lg backdrop-blur-sm animate-slide-down">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle mr-3 text-lg"></i>
                        <div>{{ form.non_field_errors }}</div>
                    </div>
                </div>
            {% endif %}

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <!-- Project Name -->
                <div class="md:col-span-2">
                    <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <i class="fas fa-tag ml-2 text-primary-600 dark:text-primary-400"></i>{{ form.name.label }}
                    </label>
                    {{ form.name }}
                    {% if form.name.errors %}
                        <div class="mt-2 text-red-600 dark:text-red-400 text-sm">
                            {% for error in form.name.errors %}
                                <p><i class="fas fa-exclamation-circle ml-1"></i>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Project Status -->
                <div>
                    <label for="{{ form.status.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <i class="fas fa-flag ml-2 text-primary-600 dark:text-primary-400"></i>{{ form.status.label }}
                    </label>
                    {{ form.status }}
                    {% if form.status.errors %}
                        <div class="mt-2 text-red-600 dark:text-red-400 text-sm">
                            {% for error in form.status.errors %}
                                <p><i class="fas fa-exclamation-circle ml-1"></i>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Project Image -->
                <div>
                    <label for="{{ form.image.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <i class="fas fa-image ml-2 text-primary-600 dark:text-primary-400"></i>{{ form.image.label }}
                    </label>
                    {{ form.image }}
                    {% if form.image.errors %}
                        <div class="mt-2 text-red-600 dark:text-red-400 text-sm">
                            {% for error in form.image.errors %}
                                <p><i class="fas fa-exclamation-circle ml-1"></i>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                    {% if project and project.image %}
                        <div class="mt-3">
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">الصورة الحالية:</p>
                            <img src="{{ project.image.url }}" alt="{{ project.name }}" class="w-32 h-32 object-cover rounded-lg shadow-md">
                        </div>
                    {% endif %}
                </div>

                <!-- Start Date -->
                <div>
                    <label for="{{ form.start_date.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <i class="fas fa-calendar-plus ml-2 text-primary-600 dark:text-primary-400"></i>{{ form.start_date.label }}
                    </label>
                    {{ form.start_date }}
                    {% if form.start_date.errors %}
                        <div class="mt-2 text-red-600 dark:text-red-400 text-sm">
                            {% for error in form.start_date.errors %}
                                <p><i class="fas fa-exclamation-circle ml-1"></i>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- End Date -->
                <div>
                    <label for="{{ form.end_date.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <i class="fas fa-calendar-check ml-2 text-primary-600 dark:text-primary-400"></i>{{ form.end_date.label }}
                    </label>
                    {{ form.end_date }}
                    {% if form.end_date.errors %}
                        <div class="mt-2 text-red-600 dark:text-red-400 text-sm">
                            {% for error in form.end_date.errors %}
                                <p><i class="fas fa-exclamation-circle ml-1"></i>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Project Description -->
                <div class="md:col-span-2">
                    <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <i class="fas fa-align-left ml-2 text-primary-600 dark:text-primary-400"></i>{{ form.description.label }}
                    </label>
                    {{ form.description }}
                    {% if form.description.errors %}
                        <div class="mt-2 text-red-600 dark:text-red-400 text-sm">
                            {% for error in form.description.errors %}
                                <p><i class="fas fa-exclamation-circle ml-1"></i>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>

            <div class="mt-12 flex justify-start space-x-4 space-x-reverse">
                <button type="submit" class="bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 btn-glow">
                    <i class="fas fa-save ml-2"></i>{{ submit_text }}
                </button>
                <a href="{% if project %}{% url 'employees:project_detail' project.pk %}{% else %}{% url 'employees:project_list' %}{% endif %}" class="bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
                    <i class="fas fa-times ml-2"></i>إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<script>
// Preview image before upload
document.getElementById('{{ form.image.id_for_label }}').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            // Remove existing preview if any
            const existingPreview = document.getElementById('image-preview');
            if (existingPreview) {
                existingPreview.remove();
            }
            
            // Create new preview
            const preview = document.createElement('div');
            preview.id = 'image-preview';
            preview.className = 'mt-3';
            preview.innerHTML = `
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">معاينة الصورة:</p>
                <img src="${e.target.result}" alt="معاينة" class="w-32 h-32 object-cover rounded-lg shadow-md">
            `;
            
            // Insert after the file input
            e.target.parentNode.appendChild(preview);
        };
        reader.readAsDataURL(file);
    }
});
</script>
{% endblock %}
