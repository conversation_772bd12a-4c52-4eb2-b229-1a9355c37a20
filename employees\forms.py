from django import forms
from django.core.exceptions import ValidationError
from .models import Employee, EmployeeService, Project, ProjectEmployee
from django.contrib.auth.models import User
import re


class EmployeeForm(forms.ModelForm):
    class Meta:
        model = Employee
        fields = [
            'first_name', 'last_name', 'email', 'phone_number', 'date_of_birth',
            'employee_id', 'position', 'department', 'hire_date',
            'address', 'city', 'state', 'postal_code', 'country',
            'monthly_salary', 'manager_payment', 'employment_status'
        ]
        labels = {
            'first_name': 'الاسم الأول',
            'last_name': 'اسم العائلة',
            'email': 'عنوان البريد الإلكتروني',
            'phone_number': 'رقم الهاتف',
            'date_of_birth': 'تاريخ الميلاد',
            'employee_id': 'رقم الموظف',
            'position': 'المنصب',
            'department': 'القسم',
            'hire_date': 'تاريخ التوظيف',
            'address': 'العنوان',
            'city': 'المدينة',
            'state': 'الولاية',
            'postal_code': 'الرمز البريدي',
            'country': 'البلد',
            'monthly_salary': 'الراتب الشهري ($)',
            'manager_payment': 'دفعة المدير ($)',
            'employment_status': 'حالة التوظيف',
        }
        widgets = {
            'first_name': forms.TextInput(attrs={
                'class': 'mt-2 block w-full rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 px-4 py-3',
                'placeholder': 'أدخل الاسم الأول'
            }),
            'last_name': forms.TextInput(attrs={
                'class': 'mt-2 block w-full rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 px-4 py-3',
                'placeholder': 'أدخل اسم العائلة'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'mt-2 block w-full rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 px-4 py-3',
                'placeholder': 'أدخل عنوان البريد الإلكتروني'
            }),
            'phone_number': forms.TextInput(attrs={
                'class': 'mt-2 block w-full rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 px-4 py-3',
                'placeholder': 'Enter phone number'
            }),
            'date_of_birth': forms.DateInput(attrs={
                'class': 'mt-2 block w-full rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white px-4 py-3',
                'type': 'date'
            }),
            'employee_id': forms.TextInput(attrs={
                'class': 'mt-2 block w-full rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 px-4 py-3',
                'placeholder': 'Enter employee ID'
            }),
            'position': forms.TextInput(attrs={
                'class': 'mt-2 block w-full rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 px-4 py-3',
                'placeholder': 'Enter position/job title'
            }),
            'department': forms.TextInput(attrs={
                'class': 'mt-2 block w-full rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 px-4 py-3',
                'placeholder': 'Enter department'
            }),
            'hire_date': forms.DateInput(attrs={
                'class': 'mt-2 block w-full rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white px-4 py-3',
                'type': 'date'
            }),
            'address': forms.Textarea(attrs={
                'class': 'mt-2 block w-full rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 px-4 py-3',
                'rows': 3,
                'placeholder': 'Enter address'
            }),
            'city': forms.TextInput(attrs={
                'class': 'mt-2 block w-full rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 px-4 py-3',
                'placeholder': 'Enter city'
            }),
            'state': forms.TextInput(attrs={
                'class': 'mt-2 block w-full rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 px-4 py-3',
                'placeholder': 'Enter state/province'
            }),
            'postal_code': forms.TextInput(attrs={
                'class': 'mt-2 block w-full rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 px-4 py-3',
                'placeholder': 'Enter postal code'
            }),
            'country': forms.TextInput(attrs={
                'class': 'mt-2 block w-full rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 px-4 py-3',
                'placeholder': 'Enter country'
            }),
            'monthly_salary': forms.NumberInput(attrs={
                'class': 'mt-2 block w-full rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 px-4 py-3',
                'step': '0.01',
                'min': '0.01',
                'placeholder': 'Enter monthly salary'
            }),
            'manager_payment': forms.NumberInput(attrs={
                'class': 'mt-2 block w-full rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 px-4 py-3',
                'step': '0.01',
                'min': '0.01',
                'placeholder': 'Enter manager payment amount'
            }),
            'employment_status': forms.Select(attrs={
                'class': 'mt-2 block w-full rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white px-4 py-3'
            }),
        }

    def clean_phone_number(self):
        phone_number = self.cleaned_data.get('phone_number')
        if phone_number:
            # Remove all non-digit characters for validation
            digits_only = re.sub(r'\D', '', phone_number)
            if len(digits_only) < 10:
                raise ValidationError('Phone number must contain at least 10 digits.')
        return phone_number

    def clean_employee_id(self):
        employee_id = self.cleaned_data.get('employee_id')
        if employee_id:
            # Check if employee_id already exists (excluding current instance if updating)
            existing = Employee.objects.filter(employee_id=employee_id)
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            if existing.exists():
                raise ValidationError('An employee with this ID already exists.')
        return employee_id

    def clean_email(self):
        email = self.cleaned_data.get('email')
        if email:
            # Check if email already exists (excluding current instance if updating)
            existing = Employee.objects.filter(email=email)
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            if existing.exists():
                raise ValidationError('An employee with this email already exists.')
        return email

    def clean(self):
        cleaned_data = super().clean()
        monthly_salary = cleaned_data.get('monthly_salary')
        manager_payment = cleaned_data.get('manager_payment')
        
        if monthly_salary and manager_payment:
            if manager_payment > monthly_salary:
                raise ValidationError('Manager payment cannot be greater than monthly salary.')
        
        return cleaned_data


class EmployeeServiceForm(forms.ModelForm):
    class Meta:
        model = EmployeeService
        fields = ['employee', 'service_date', 'service_description', 'service_amount']
        labels = {
            'employee': 'اسم الموظف',
            'service_date': 'تاريخ الخدمة',
            'service_description': 'وصف الخدمة',
            'service_amount': 'مبلغ الخدمة ($)',
        }
        widgets = {
            'employee': forms.Select(attrs={
                'class': 'mt-2 block w-full rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white px-4 py-3'
            }),
            'service_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'mt-2 block w-full rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white px-4 py-3'
            }),
            'service_description': forms.Textarea(attrs={
                'class': 'mt-2 block w-full rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 px-4 py-3',
                'rows': 4,
                'placeholder': 'اكتب وصفاً تفصيلياً للخدمة التي قدمها الموظف للشركة...'
            }),
            'service_amount': forms.NumberInput(attrs={
                'class': 'mt-2 block w-full rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 px-4 py-3',
                'step': '0.01',
                'min': '0.01',
                'placeholder': 'أدخل مبلغ الخدمة'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Only show active employees in the dropdown
        self.fields['employee'].queryset = Employee.objects.filter(employment_status='active').order_by('first_name', 'last_name')

    def clean_service_amount(self):
        service_amount = self.cleaned_data.get('service_amount')
        if service_amount and service_amount <= 0:
            raise ValidationError('مبلغ الخدمة يجب أن يكون أكبر من صفر.')
        return service_amount


class ProjectForm(forms.ModelForm):
    """Form for creating and editing projects."""

    class Meta:
        model = Project
        fields = ['name', 'description', 'image', 'status', 'start_date', 'end_date']
        labels = {
            'name': 'اسم المشروع',
            'description': 'وصف المشروع',
            'image': 'صورة المشروع',
            'status': 'حالة المشروع',
            'start_date': 'تاريخ البدء',
            'end_date': 'تاريخ الانتهاء المتوقع',
        }
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'mt-2 block w-full rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 px-4 py-3',
                'placeholder': 'أدخل اسم المشروع'
            }),
            'description': forms.Textarea(attrs={
                'class': 'mt-2 block w-full rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 px-4 py-3',
                'rows': 5,
                'placeholder': 'اكتب وصفاً تفصيلياً للمشروع...'
            }),
            'image': forms.FileInput(attrs={
                'class': 'mt-2 block w-full text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-xl cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500 px-4 py-3',
                'accept': 'image/*'
            }),
            'status': forms.Select(attrs={
                'class': 'mt-2 block w-full rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white px-4 py-3'
            }),
            'start_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'mt-2 block w-full rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white px-4 py-3'
            }),
            'end_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'mt-2 block w-full rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white px-4 py-3'
            }),
        }

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date:
            if end_date < start_date:
                raise ValidationError('تاريخ الانتهاء يجب أن يكون بعد تاريخ البدء.')

        return cleaned_data


class ProjectEmployeeForm(forms.ModelForm):
    """Form for adding employees to projects."""

    class Meta:
        model = ProjectEmployee
        fields = ['employee', 'role', 'assigned_date']
        labels = {
            'employee': 'الموظف',
            'role': 'الدور في المشروع',
            'assigned_date': 'تاريخ التعيين',
        }
        widgets = {
            'employee': forms.Select(attrs={
                'class': 'mt-2 block w-full rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white px-4 py-3'
            }),
            'role': forms.TextInput(attrs={
                'class': 'mt-2 block w-full rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 px-4 py-3',
                'placeholder': 'مثال: مطور، مصمم، مدير فريق...'
            }),
            'assigned_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'mt-2 block w-full rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white px-4 py-3'
            }),
        }

    def __init__(self, *args, **kwargs):
        project = kwargs.pop('project', None)
        super().__init__(*args, **kwargs)

        # Only show active employees
        self.fields['employee'].queryset = Employee.objects.filter(employment_status='active').order_by('first_name', 'last_name')

        # If project is provided, exclude employees already assigned to this project
        if project:
            assigned_employee_ids = ProjectEmployee.objects.filter(
                project=project,
                is_active=True
            ).values_list('employee_id', flat=True)
            self.fields['employee'].queryset = self.fields['employee'].queryset.exclude(
                id__in=assigned_employee_ids
            )
