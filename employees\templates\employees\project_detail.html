{% extends 'base.html' %}

{% block title %}{{ project.name }} - Employee Management System{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Project Header -->
    <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg shadow-2xl rounded-2xl border border-gray-200/50 dark:border-gray-700/50 hover-lift mb-8">
        <div class="px-8 py-6 border-b border-gray-200/50 dark:border-gray-700/50 bg-gradient-to-r from-primary-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 rounded-t-2xl">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <i class="fas fa-project-diagram text-3xl text-primary-600 dark:text-primary-400 float"></i>
                        <div class="absolute -inset-2 bg-primary-600/20 rounded-full blur opacity-50"></div>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold bg-gradient-to-r from-primary-600 to-primary-800 dark:from-primary-400 dark:to-primary-600 bg-clip-text text-transparent">
                            {{ project.name }}
                        </h1>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">تفاصيل المشروع</p>
                    </div>
                </div>
                <div class="flex gap-3">
                    <a href="{% url 'employees:project_update' project.pk %}" class="bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-700 hover:to-yellow-800 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
                        <i class="fas fa-edit ml-2"></i>تحديث
                    </a>
                    <a href="{% url 'employees:project_delete' project.pk %}" class="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
                        <i class="fas fa-trash ml-2"></i>حذف
                    </a>
                    <a href="{% url 'employees:project_list' %}" class="bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
                        <i class="fas fa-arrow-right ml-2"></i>العودة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Project Information -->
        <div class="lg:col-span-2">
            <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg shadow-2xl rounded-2xl border border-gray-200/50 dark:border-gray-700/50 hover-lift mb-8">
                <div class="px-8 py-6 border-b border-gray-200/50 dark:border-gray-700/50">
                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white">معلومات المشروع</h2>
                </div>
                <div class="p-8">
                    {% if project.image %}
                        <div class="mb-6">
                            <img src="{{ project.image.url }}" alt="{{ project.name }}" class="w-full h-64 object-cover rounded-xl shadow-lg">
                        </div>
                    {% endif %}

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">حالة المشروع</label>
                            <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium
                                {% if project.status == 'completed' %}bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                {% elif project.status == 'in_progress' %}bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                {% elif project.status == 'planning' %}bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                                {% elif project.status == 'on_hold' %}bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200
                                {% else %}bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200{% endif %}">
                                {{ project.get_status_display }}
                            </span>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">مدير المشروع</label>
                            <p class="text-gray-900 dark:text-white">{{ project.manager.username }}</p>
                        </div>
                        {% if project.start_date %}
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تاريخ البدء</label>
                            <p class="text-gray-900 dark:text-white">{{ project.start_date|date:"Y/m/d" }}</p>
                        </div>
                        {% endif %}
                        {% if project.end_date %}
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تاريخ الانتهاء المتوقع</label>
                            <p class="text-gray-900 dark:text-white">{{ project.end_date|date:"Y/m/d" }}</p>
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">وصف المشروع</label>
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-4">
                            <p class="text-gray-900 dark:text-white whitespace-pre-wrap">{{ project.description }}</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-500 dark:text-gray-400">
                        <div>
                            <i class="fas fa-calendar-plus ml-2"></i>
                            <span>تاريخ الإنشاء: {{ project.created_at|date:"Y/m/d H:i" }}</span>
                        </div>
                        <div>
                            <i class="fas fa-calendar-edit ml-2"></i>
                            <span>آخر تحديث: {{ project.updated_at|date:"Y/m/d H:i" }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Project Team -->
        <div>
            <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg shadow-2xl rounded-2xl border border-gray-200/50 dark:border-gray-700/50 hover-lift">
                <div class="px-6 py-4 border-b border-gray-200/50 dark:border-gray-700/50 flex items-center justify-between">
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white">فريق المشروع</h3>
                    <a href="{% url 'employees:project_add_employee' project.pk %}" class="bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white px-4 py-2 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 text-sm">
                        <i class="fas fa-plus ml-1"></i>إضافة موظف
                    </a>
                </div>
                <div class="p-6">
                    {% if project_employees %}
                        <div class="space-y-4">
                            {% for project_employee in project_employees %}
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-4 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h4 class="font-semibold text-gray-900 dark:text-white">
                                                <a href="{% url 'employees:employee_detail' project_employee.employee.pk %}" class="hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200">
                                                    {{ project_employee.employee.full_name }}
                                                </a>
                                            </h4>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">{{ project_employee.employee.position }}</p>
                                            {% if project_employee.role %}
                                                <p class="text-sm text-primary-600 dark:text-primary-400 font-medium">{{ project_employee.role }}</p>
                                            {% endif %}
                                            <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">
                                                <i class="fas fa-calendar ml-1"></i>
                                                منذ {{ project_employee.assigned_date|date:"Y/m/d" }}
                                            </p>
                                        </div>
                                        <div class="flex gap-2">
                                            <a href="{% url 'employees:project_employee_update' project.pk project_employee.employee.pk %}" 
                                               class="text-yellow-600 hover:text-yellow-800 dark:text-yellow-400 dark:hover:text-yellow-300 transition-colors duration-200" 
                                               title="تحديث الدور">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'employees:project_remove_employee' project.pk project_employee.employee.pk %}" 
                                               class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 transition-colors duration-200" 
                                               title="إزالة من المشروع">
                                                <i class="fas fa-times"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-8">
                            <i class="fas fa-users text-4xl text-gray-400 dark:text-gray-600 mb-4"></i>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">لا يوجد موظفون مُعيَّنون لهذا المشروع</p>
                            <a href="{% url 'employees:project_add_employee' project.pk %}" class="bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
                                <i class="fas fa-plus ml-2"></i>إضافة موظف
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Project Statistics -->
            <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg shadow-2xl rounded-2xl border border-gray-200/50 dark:border-gray-700/50 hover-lift mt-6">
                <div class="px-6 py-4 border-b border-gray-200/50 dark:border-gray-700/50">
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white">إحصائيات المشروع</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600 dark:text-gray-400">عدد الموظفين</span>
                            <span class="font-semibold text-gray-900 dark:text-white">{{ employees_count }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600 dark:text-gray-400">حالة المشروع</span>
                            <span class="font-semibold {% if project.is_active %}text-green-600 dark:text-green-400{% else %}text-gray-600 dark:text-gray-400{% endif %}">
                                {% if project.is_active %}نشط{% else %}غير نشط{% endif %}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
