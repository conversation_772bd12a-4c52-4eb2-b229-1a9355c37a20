{% extends 'base.html' %}

{% block title %}حذف المشروع: {{ project.name }} - Employee Management System{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg shadow-2xl rounded-2xl border border-gray-200/50 dark:border-gray-700/50 hover-lift">
        <div class="px-8 py-6 border-b border-gray-200/50 dark:border-gray-700/50 bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/50 dark:to-red-800/50 rounded-t-2xl">
            <div class="flex items-center space-x-4">
                <div class="relative">
                    <i class="fas fa-exclamation-triangle text-3xl text-red-600 dark:text-red-400 float"></i>
                    <div class="absolute -inset-2 bg-red-600/20 rounded-full blur opacity-50"></div>
                </div>
                <div>
                    <h1 class="text-3xl font-bold bg-gradient-to-r from-red-600 to-red-800 dark:from-red-400 dark:to-red-600 bg-clip-text text-transparent">
                        تأكيد حذف المشروع
                    </h1>
                    <p class="text-sm text-red-600 dark:text-red-400 mt-1">هذا الإجراء لا يمكن التراجع عنه</p>
                </div>
            </div>
        </div>

        <div class="p-8">
            <div class="mb-8">
                <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-6">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-red-400 text-xl"></i>
                        </div>
                        <div class="mr-3">
                            <h3 class="text-lg font-medium text-red-800 dark:text-red-200 mb-2">
                                هل أنت متأكد من حذف هذا المشروع؟
                            </h3>
                            <p class="text-red-700 dark:text-red-300 mb-4">
                                سيتم حذف المشروع "{{ project.name }}" نهائياً من النظام. هذا الإجراء لا يمكن التراجع عنه.
                            </p>
                            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-red-200 dark:border-red-700">
                                <h4 class="font-semibold text-gray-900 dark:text-white mb-2">ما سيتم حذفه:</h4>
                                <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                    <li><i class="fas fa-check text-red-500 ml-2"></i>جميع معلومات المشروع</li>
                                    <li><i class="fas fa-check text-red-500 ml-2"></i>ربط الموظفين بالمشروع</li>
                                    {% if project.image %}
                                    <li><i class="fas fa-check text-red-500 ml-2"></i>صورة المشروع</li>
                                    {% endif %}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Project Details -->
            <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 mb-8">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">تفاصيل المشروع المراد حذفه:</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">اسم المشروع</label>
                        <p class="text-gray-900 dark:text-white font-semibold">{{ project.name }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">حالة المشروع</label>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                            {% if project.status == 'completed' %}bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                            {% elif project.status == 'in_progress' %}bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                            {% elif project.status == 'planning' %}bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                            {% elif project.status == 'on_hold' %}bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200
                            {% else %}bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200{% endif %}">
                            {{ project.get_status_display }}
                        </span>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">مدير المشروع</label>
                        <p class="text-gray-900 dark:text-white">{{ project.manager.username }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">عدد الموظفين</label>
                        <p class="text-gray-900 dark:text-white">{{ project.employees_count }} موظف</p>
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">تاريخ الإنشاء</label>
                        <p class="text-gray-900 dark:text-white">{{ project.created_at|date:"Y/m/d H:i" }}</p>
                    </div>
                </div>

                {% if project.image %}
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">صورة المشروع</label>
                    <img src="{{ project.image.url }}" alt="{{ project.name }}" class="w-32 h-32 object-cover rounded-lg shadow-md">
                </div>
                {% endif %}
            </div>

            <!-- Action Buttons -->
            <form method="post" class="flex justify-center space-x-4 space-x-reverse">
                {% csrf_token %}
                <button type="submit" class="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 btn-glow">
                    <i class="fas fa-trash ml-2"></i>نعم، احذف المشروع
                </button>
                <a href="{% url 'employees:project_detail' project.pk %}" class="bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
                    <i class="fas fa-times ml-2"></i>إلغاء
                </a>
            </form>
        </div>
    </div>
</div>
{% endblock %}
