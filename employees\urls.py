from django.urls import path
from . import views

app_name = 'employees'

urlpatterns = [
    # Dashboard
    path('', views.dashboard, name='dashboard'),

    # Employee URLs
    path('employees/', views.employee_list, name='employee_list'),
    path('employee/<int:pk>/', views.employee_detail, name='employee_detail'),
    path('employee/create/', views.employee_create, name='employee_create'),
    path('employee/<int:pk>/edit/', views.employee_update, name='employee_update'),
    path('employee/<int:pk>/delete/', views.employee_delete, name='employee_delete'),

    # Employee Services URLs
    path('services/', views.service_list, name='service_list'),
    path('service/<int:pk>/', views.service_detail, name='service_detail'),
    path('service/create/', views.service_create, name='service_create'),
    path('service/<int:pk>/edit/', views.service_update, name='service_update'),
    path('service/<int:pk>/delete/', views.service_delete, name='service_delete'),

    # Project URLs
    path('projects/', views.project_list, name='project_list'),
    path('project/<int:pk>/', views.project_detail, name='project_detail'),
    path('project/create/', views.project_create, name='project_create'),
    path('project/<int:pk>/edit/', views.project_update, name='project_update'),
    path('project/<int:pk>/delete/', views.project_delete, name='project_delete'),

    # Project Employee Management URLs
    path('project/<int:project_pk>/add-employee/', views.project_add_employee, name='project_add_employee'),
    path('project/<int:project_pk>/employee/<int:employee_pk>/remove/', views.project_remove_employee, name='project_remove_employee'),
    path('project/<int:project_pk>/employee/<int:employee_pk>/edit/', views.project_employee_update, name='project_employee_update'),
]
