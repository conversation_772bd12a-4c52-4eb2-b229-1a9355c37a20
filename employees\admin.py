from django.contrib import admin
from .models import Employee, EmployeeService, Project, ProjectEmployee


@admin.register(Employee)
class EmployeeAdmin(admin.ModelAdmin):
    list_display = ['employee_id', 'full_name', 'position', 'department', 'monthly_salary', 'employment_status', 'hire_date']
    list_filter = ['employment_status', 'department', 'hire_date']
    search_fields = ['first_name', 'last_name', 'employee_id', 'email', 'position']
    ordering = ['last_name', 'first_name']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('Personal Information', {
            'fields': ('first_name', 'last_name', 'email', 'phone_number', 'date_of_birth')
        }),
        ('Employment Information', {
            'fields': ('employee_id', 'position', 'department', 'hire_date', 'employment_status')
        }),
        ('Address Information', {
            'fields': ('address', 'city', 'state', 'postal_code', 'country'),
            'classes': ('collapse',)
        }),
        ('Salary Information', {
            'fields': ('monthly_salary', 'manager_payment')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(EmployeeService)
class EmployeeServiceAdmin(admin.ModelAdmin):
    list_display = ['employee', 'service_date', 'service_amount', 'created_at']
    list_filter = ['service_date', 'created_at', 'employee__department']
    search_fields = ['employee__first_name', 'employee__last_name', 'employee__employee_id', 'service_description']
    ordering = ['-service_date', '-created_at']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('Service Information', {
            'fields': ('employee', 'service_date', 'service_description', 'service_amount')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


class ProjectEmployeeInline(admin.TabularInline):
    model = ProjectEmployee
    extra = 0
    fields = ['employee', 'role', 'assigned_date', 'is_active']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(Project)
class ProjectAdmin(admin.ModelAdmin):
    list_display = ['name', 'status', 'manager', 'employees_count', 'start_date', 'end_date', 'created_at']
    list_filter = ['status', 'start_date', 'end_date', 'created_at', 'manager']
    search_fields = ['name', 'description', 'manager__username']
    ordering = ['-created_at']
    readonly_fields = ['created_at', 'updated_at', 'employees_count']
    inlines = [ProjectEmployeeInline]

    fieldsets = (
        ('Project Information', {
            'fields': ('name', 'description', 'image', 'status', 'manager')
        }),
        ('Project Timeline', {
            'fields': ('start_date', 'end_date')
        }),
        ('Statistics', {
            'fields': ('employees_count',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def employees_count(self, obj):
        return obj.employees_count
    employees_count.short_description = 'عدد الموظفين'


@admin.register(ProjectEmployee)
class ProjectEmployeeAdmin(admin.ModelAdmin):
    list_display = ['project', 'employee', 'role', 'assigned_date', 'is_active', 'created_at']
    list_filter = ['is_active', 'assigned_date', 'created_at', 'project__status']
    search_fields = ['project__name', 'employee__first_name', 'employee__last_name', 'role']
    ordering = ['-assigned_date', '-created_at']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('Assignment Information', {
            'fields': ('project', 'employee', 'role', 'assigned_date', 'removed_date', 'is_active')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
