from django.db import models
from django.core.validators import MinValueValidator
from decimal import Decimal
from django.utils import timezone


class Employee(models.Model):
    # Personal Information
    first_name = models.CharField(max_length=100, verbose_name="First Name")
    last_name = models.Char<PERSON>ield(max_length=100, verbose_name="Last Name")
    email = models.EmailField(unique=True, verbose_name="Email Address")
    phone_number = models.CharField(max_length=20, blank=True, verbose_name="Phone Number")
    date_of_birth = models.DateField(null=True, blank=True, verbose_name="Date of Birth")

    # Employment Information
    employee_id = models.Char<PERSON>ield(max_length=20, unique=True, verbose_name="Employee ID")
    position = models.Char<PERSON>ield(max_length=100, verbose_name="Position/Job Title")
    department = models.CharField(max_length=100, blank=True, verbose_name="Department")
    hire_date = models.DateField(verbose_name="Hire Date")

    # Address Information
    address = models.TextField(blank=True, verbose_name="Address")
    city = models.CharField(max_length=100, blank=True, verbose_name="City")
    state = models.CharField(max_length=100, blank=True, verbose_name="State/Province")
    postal_code = models.CharField(max_length=20, blank=True, verbose_name="Postal Code")
    country = models.CharField(max_length=100, blank=True, verbose_name="Country")

    # Salary Information
    monthly_salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        verbose_name="Monthly Salary"
    )
    manager_payment = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        verbose_name="Manager Payment Amount",
        help_text="Amount that the manager must pay the employee"
    )

    # Employment Status
    EMPLOYMENT_STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('terminated', 'Terminated'),
        ('on_leave', 'On Leave'),
    ]
    employment_status = models.CharField(
        max_length=20,
        choices=EMPLOYMENT_STATUS_CHOICES,
        default='active',
        verbose_name="Employment Status"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['last_name', 'first_name']
        verbose_name = "Employee"
        verbose_name_plural = "Employees"

    def __str__(self):
        return f"{self.first_name} {self.last_name} - {self.position}"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def annual_salary(self):
        return self.monthly_salary * 12

    @property
    def annual_manager_payment(self):
        return self.manager_payment * 12


class EmployeeService(models.Model):
    """Model to track services provided by employees to the company."""

    # Employee who provided the service
    employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='services',
        verbose_name="الموظف"
    )

    # Service date
    service_date = models.DateField(verbose_name="تاريخ الخدمة")

    # Service description
    service_description = models.TextField(
        verbose_name="وصف الخدمة",
        help_text="وصف تفصيلي للخدمة التي قدمها الموظف للشركة"
    )

    # Service amount
    service_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        verbose_name="مبلغ الخدمة",
        help_text="المبلغ المالي للخدمة المقدمة"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-service_date', '-created_at']
        verbose_name = "خدمة موظف"
        verbose_name_plural = "خدمات الموظفين"

    def __str__(self):
        return f"{self.employee.full_name} - {self.service_date} - ${self.service_amount}"


class Project(models.Model):
    """Model to represent company projects."""

    # Project basic information
    name = models.CharField(
        max_length=200,
        verbose_name="اسم المشروع",
        help_text="اسم المشروع"
    )

    description = models.TextField(
        verbose_name="وصف المشروع",
        help_text="وصف تفصيلي للمشروع"
    )

    # Project image
    image = models.ImageField(
        upload_to='projects/',
        blank=True,
        null=True,
        verbose_name="صورة المشروع",
        help_text="صورة توضيحية للمشروع"
    )

    # Project status
    PROJECT_STATUS_CHOICES = [
        ('planning', 'في مرحلة التخطيط'),
        ('in_progress', 'قيد التنفيذ'),
        ('completed', 'مكتمل'),
        ('on_hold', 'متوقف مؤقتاً'),
        ('cancelled', 'ملغي'),
    ]

    status = models.CharField(
        max_length=20,
        choices=PROJECT_STATUS_CHOICES,
        default='planning',
        verbose_name="حالة المشروع"
    )

    # Project dates
    start_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="تاريخ البدء"
    )

    end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="تاريخ الانتهاء المتوقع"
    )

    # Project manager (owner)
    manager = models.ForeignKey(
        'auth.User',
        on_delete=models.CASCADE,
        related_name='managed_projects',
        verbose_name="مدير المشروع",
        help_text="المستخدم المسؤول عن إدارة المشروع"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ آخر تحديث")

    class Meta:
        ordering = ['-created_at']
        verbose_name = "مشروع"
        verbose_name_plural = "المشاريع"

    def __str__(self):
        return self.name

    @property
    def employees_count(self):
        """Return the number of employees assigned to this project."""
        return self.project_employees.count()

    @property
    def is_active(self):
        """Check if the project is currently active."""
        return self.status in ['planning', 'in_progress']


class ProjectEmployee(models.Model):
    """Model to represent the relationship between projects and employees."""

    project = models.ForeignKey(
        Project,
        on_delete=models.CASCADE,
        related_name='project_employees',
        verbose_name="المشروع"
    )

    employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='employee_projects',
        verbose_name="الموظف"
    )

    # Role in the project
    role = models.CharField(
        max_length=100,
        blank=True,
        verbose_name="الدور في المشروع",
        help_text="دور الموظف في هذا المشروع"
    )

    # Assignment dates
    assigned_date = models.DateField(
        default=timezone.now,
        verbose_name="تاريخ التعيين"
    )

    removed_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="تاريخ الإزالة"
    )

    # Status
    is_active = models.BooleanField(
        default=True,
        verbose_name="نشط في المشروع"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['project', 'employee']
        ordering = ['-assigned_date']
        verbose_name = "موظف المشروع"
        verbose_name_plural = "موظفي المشاريع"

    def __str__(self):
        return f"{self.project.name} - {self.employee.full_name}"
