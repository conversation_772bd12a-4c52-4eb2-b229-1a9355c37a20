from django.shortcuts import render, get_object_or_404, redirect
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Sum
from django.contrib.auth.decorators import login_required
from django.utils import timezone
from .models import Employee, EmployeeService, Project, ProjectEmployee
from .forms import EmployeeForm, EmployeeServiceForm, ProjectForm, ProjectEmployeeForm


def dashboard(request):
    """Display dashboard with overview of employees, services, and projects."""
    total_employees = Employee.objects.count()
    active_employees = Employee.objects.filter(employment_status='active').count()
    total_services = EmployeeService.objects.count()
    total_service_amount = EmployeeService.objects.aggregate(
        total=Sum('service_amount')
    )['total'] or 0

    # Project statistics
    total_projects = Project.objects.count()
    active_projects = Project.objects.filter(status__in=['planning', 'in_progress']).count()
    completed_projects = Project.objects.filter(status='completed').count()

    context = {
        'total_employees': total_employees,
        'active_employees': active_employees,
        'total_services': total_services,
        'total_service_amount': total_service_amount,
        'total_projects': total_projects,
        'active_projects': active_projects,
        'completed_projects': completed_projects,
    }
    return render(request, 'employees/dashboard.html', context)


def employee_list(request):
    """Display a list of all employees with search and pagination."""
    search_query = request.GET.get('search', '')
    employees = Employee.objects.all()

    if search_query:
        employees = employees.filter(
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(employee_id__icontains=search_query) |
            Q(position__icontains=search_query) |
            Q(department__icontains=search_query) |
            Q(email__icontains=search_query)
        )

    # Pagination
    paginator = Paginator(employees, 10)  # Show 10 employees per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'total_employees': Employee.objects.count(),
    }
    return render(request, 'employees/employee_list.html', context)


def employee_detail(request, pk):
    """Display detailed information about a specific employee."""
    employee = get_object_or_404(Employee, pk=pk)

    # Calculate total services amount for this employee
    total_services_amount = employee.services.aggregate(
        total=Sum('service_amount')
    )['total'] or 0

    # Get recent services for this employee
    recent_services = employee.services.order_by('-service_date')[:5]

    context = {
        'employee': employee,
        'total_services_amount': total_services_amount,
        'recent_services': recent_services,
        'services_count': employee.services.count(),
    }
    return render(request, 'employees/employee_detail.html', context)


def employee_create(request):
    """Create a new employee."""
    if request.method == 'POST':
        form = EmployeeForm(request.POST)
        if form.is_valid():
            employee = form.save()
            messages.success(request, f'تم إنشاء الموظف {employee.full_name} بنجاح.')
            return redirect('employees:employee_detail', pk=employee.pk)
    else:
        form = EmployeeForm()

    context = {
        'form': form,
        'title': 'إضافة موظف جديد',
        'submit_text': 'إنشاء موظف',
    }
    return render(request, 'employees/employee_form.html', context)


def employee_update(request, pk):
    """Update an existing employee."""
    employee = get_object_or_404(Employee, pk=pk)

    if request.method == 'POST':
        form = EmployeeForm(request.POST, instance=employee)
        if form.is_valid():
            employee = form.save()
            messages.success(request, f'تم تحديث الموظف {employee.full_name} بنجاح.')
            return redirect('employees:employee_detail', pk=employee.pk)
    else:
        form = EmployeeForm(instance=employee)

    context = {
        'form': form,
        'employee': employee,
        'title': f'تعديل {employee.full_name}',
        'submit_text': 'تحديث الموظف',
    }
    return render(request, 'employees/employee_form.html', context)


def employee_delete(request, pk):
    """Delete an employee."""
    employee = get_object_or_404(Employee, pk=pk)

    if request.method == 'POST':
        employee_name = employee.full_name
        employee.delete()
        messages.success(request, f'تم حذف الموظف {employee_name} بنجاح.')
        return redirect('employees:employee_list')

    context = {
        'employee': employee,
    }
    return render(request, 'employees/employee_confirm_delete.html', context)


# Employee Services Views
def service_list(request):
    """Display a list of all employee services with search and pagination."""
    search_query = request.GET.get('search', '')
    employee_filter = request.GET.get('employee', '')
    services = EmployeeService.objects.select_related('employee').all()

    # Filter by employee if specified
    if employee_filter:
        services = services.filter(employee__pk=employee_filter)

    if search_query:
        services = services.filter(
            Q(employee__first_name__icontains=search_query) |
            Q(employee__last_name__icontains=search_query) |
            Q(service_description__icontains=search_query) |
            Q(employee__employee_id__icontains=search_query)
        )

    # Order by service date (newest first)
    services = services.order_by('-service_date', '-created_at')

    # Pagination
    paginator = Paginator(services, 10)  # Show 10 services per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get filtered employee info if filtering by employee
    filtered_employee = None
    if employee_filter:
        try:
            filtered_employee = Employee.objects.get(pk=employee_filter)
        except Employee.DoesNotExist:
            pass

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'employee_filter': employee_filter,
        'filtered_employee': filtered_employee,
        'total_services': EmployeeService.objects.count(),
    }
    return render(request, 'employees/service_list.html', context)


def service_detail(request, pk):
    """Display detailed information about a specific service."""
    service = get_object_or_404(EmployeeService, pk=pk)
    context = {
        'service': service,
    }
    return render(request, 'employees/service_detail.html', context)


def service_create(request):
    """Create a new employee service."""
    # Get employee from URL parameter if provided
    employee_id = request.GET.get('employee')
    initial_data = {}
    if employee_id:
        try:
            employee = Employee.objects.get(pk=employee_id)
            initial_data['employee'] = employee
        except Employee.DoesNotExist:
            pass

    if request.method == 'POST':
        form = EmployeeServiceForm(request.POST)
        if form.is_valid():
            service = form.save()
            messages.success(request, f'تم إنشاء خدمة للموظف {service.employee.full_name} بنجاح.')
            return redirect('employees:service_detail', pk=service.pk)
    else:
        form = EmployeeServiceForm(initial=initial_data)

    context = {
        'form': form,
        'title': 'إضافة خدمة جديدة',
        'submit_text': 'إنشاء خدمة',
    }
    return render(request, 'employees/service_form.html', context)


def service_update(request, pk):
    """Update an existing employee service."""
    service = get_object_or_404(EmployeeService, pk=pk)

    if request.method == 'POST':
        form = EmployeeServiceForm(request.POST, instance=service)
        if form.is_valid():
            service = form.save()
            messages.success(request, f'تم تحديث خدمة الموظف {service.employee.full_name} بنجاح.')
            return redirect('employees:service_detail', pk=service.pk)
    else:
        form = EmployeeServiceForm(instance=service)

    context = {
        'form': form,
        'service': service,
        'title': f'تعديل خدمة {service.employee.full_name}',
        'submit_text': 'تحديث الخدمة',
    }
    return render(request, 'employees/service_form.html', context)


def service_delete(request, pk):
    """Delete an employee service."""
    service = get_object_or_404(EmployeeService, pk=pk)

    if request.method == 'POST':
        employee_name = service.employee.full_name
        service.delete()
        messages.success(request, f'تم حذف خدمة الموظف {employee_name} بنجاح.')
        return redirect('employees:service_list')

    context = {
        'service': service,
    }
    return render(request, 'employees/service_confirm_delete.html', context)


# Project Views
def project_list(request):
    """Display a list of all projects with search and pagination."""
    search_query = request.GET.get('search', '')
    status_filter = request.GET.get('status', '')
    projects = Project.objects.all()

    # Filter by status if specified
    if status_filter:
        projects = projects.filter(status=status_filter)

    if search_query:
        projects = projects.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query) |
            Q(manager__username__icontains=search_query)
        )

    # Order by creation date (newest first)
    projects = projects.order_by('-created_at')

    # Pagination
    paginator = Paginator(projects, 12)  # Show 12 projects per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
        'total_projects': Project.objects.count(),
        'project_status_choices': Project.PROJECT_STATUS_CHOICES,
    }
    return render(request, 'employees/project_list.html', context)


def project_detail(request, pk):
    """Display detailed information about a specific project."""
    project = get_object_or_404(Project, pk=pk)

    # Get project employees
    project_employees = ProjectEmployee.objects.filter(
        project=project,
        is_active=True
    ).select_related('employee')

    context = {
        'project': project,
        'project_employees': project_employees,
        'employees_count': project_employees.count(),
    }
    return render(request, 'employees/project_detail.html', context)


def project_create(request):
    """Create a new project."""
    if request.method == 'POST':
        form = ProjectForm(request.POST, request.FILES)
        if form.is_valid():
            project = form.save(commit=False)
            # Set the current user as the project manager
            if request.user.is_authenticated:
                project.manager = request.user
            else:
                # If no user is authenticated, you might want to handle this differently
                # For now, we'll create a default user or handle it as needed
                from django.contrib.auth.models import User
                default_user, created = User.objects.get_or_create(
                    username='admin',
                    defaults={'email': '<EMAIL>'}
                )
                project.manager = default_user
            project.save()
            messages.success(request, f'تم إنشاء المشروع "{project.name}" بنجاح.')
            return redirect('employees:project_detail', pk=project.pk)
    else:
        form = ProjectForm()

    context = {
        'form': form,
        'title': 'إنشاء مشروع جديد',
        'submit_text': 'إنشاء المشروع',
    }
    return render(request, 'employees/project_form.html', context)


def project_update(request, pk):
    """Update an existing project."""
    project = get_object_or_404(Project, pk=pk)

    if request.method == 'POST':
        form = ProjectForm(request.POST, request.FILES, instance=project)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث المشروع "{project.name}" بنجاح.')
            return redirect('employees:project_detail', pk=project.pk)
    else:
        form = ProjectForm(instance=project)

    context = {
        'form': form,
        'project': project,
        'title': f'تحديث المشروع: {project.name}',
        'submit_text': 'حفظ التغييرات',
    }
    return render(request, 'employees/project_form.html', context)


def project_delete(request, pk):
    """Delete a project."""
    project = get_object_or_404(Project, pk=pk)

    if request.method == 'POST':
        project_name = project.name
        project.delete()
        messages.success(request, f'تم حذف المشروع "{project_name}" بنجاح.')
        return redirect('employees:project_list')

    context = {
        'project': project,
    }
    return render(request, 'employees/project_confirm_delete.html', context)


# Project Employee Management Views
def project_add_employee(request, project_pk):
    """Add an employee to a project."""
    project = get_object_or_404(Project, pk=project_pk)

    if request.method == 'POST':
        form = ProjectEmployeeForm(request.POST, project=project)
        if form.is_valid():
            project_employee = form.save(commit=False)
            project_employee.project = project
            project_employee.save()
            messages.success(request, f'تم إضافة الموظف {project_employee.employee.full_name} إلى المشروع بنجاح.')
            return redirect('employees:project_detail', pk=project.pk)
    else:
        form = ProjectEmployeeForm(project=project)

    context = {
        'form': form,
        'project': project,
        'title': f'إضافة موظف إلى المشروع: {project.name}',
        'submit_text': 'إضافة الموظف',
    }
    return render(request, 'employees/project_employee_form.html', context)


def project_remove_employee(request, project_pk, employee_pk):
    """Remove an employee from a project."""
    project = get_object_or_404(Project, pk=project_pk)
    project_employee = get_object_or_404(
        ProjectEmployee,
        project=project,
        employee_id=employee_pk,
        is_active=True
    )

    if request.method == 'POST':
        employee_name = project_employee.employee.full_name
        project_employee.is_active = False
        project_employee.removed_date = timezone.now().date()
        project_employee.save()
        messages.success(request, f'تم إزالة الموظف {employee_name} من المشروع بنجاح.')
        return redirect('employees:project_detail', pk=project.pk)

    context = {
        'project': project,
        'project_employee': project_employee,
    }
    return render(request, 'employees/project_employee_confirm_remove.html', context)


def project_employee_update(request, project_pk, employee_pk):
    """Update employee role in a project."""
    project = get_object_or_404(Project, pk=project_pk)
    project_employee = get_object_or_404(
        ProjectEmployee,
        project=project,
        employee_id=employee_pk,
        is_active=True
    )

    if request.method == 'POST':
        form = ProjectEmployeeForm(request.POST, instance=project_employee, project=project)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث دور الموظف {project_employee.employee.full_name} في المشروع بنجاح.')
            return redirect('employees:project_detail', pk=project.pk)
    else:
        form = ProjectEmployeeForm(instance=project_employee, project=project)

    context = {
        'form': form,
        'project': project,
        'project_employee': project_employee,
        'title': f'تحديث دور الموظف: {project_employee.employee.full_name}',
        'submit_text': 'حفظ التغييرات',
    }
    return render(request, 'employees/project_employee_form.html', context)
