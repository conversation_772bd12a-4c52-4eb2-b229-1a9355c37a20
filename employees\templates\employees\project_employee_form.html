{% extends 'base.html' %}

{% block title %}{{ title }} - Employee Management System{% endblock %}

{% block content %}
<div class="max-w-3xl mx-auto">
    <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg shadow-2xl rounded-2xl border border-gray-200/50 dark:border-gray-700/50 hover-lift">
        <div class="px-8 py-6 border-b border-gray-200/50 dark:border-gray-700/50 bg-gradient-to-r from-primary-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 rounded-t-2xl">
            <div class="flex items-center space-x-4">
                <div class="relative">
                    <i class="fas fa-user-plus text-3xl text-primary-600 dark:text-primary-400 float"></i>
                    <div class="absolute -inset-2 bg-primary-600/20 rounded-full blur opacity-50"></div>
                </div>
                <div>
                    <h1 class="text-3xl font-bold bg-gradient-to-r from-primary-600 to-primary-800 dark:from-primary-400 dark:to-primary-600 bg-clip-text text-transparent">
                        {{ title }}
                    </h1>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">المشروع: {{ project.name }}</p>
                </div>
            </div>
        </div>

        <form method="post" class="p-8">
            {% csrf_token %}

            {% if form.non_field_errors %}
                <div class="mb-6 p-6 bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/50 dark:to-red-800/50 border border-red-200 dark:border-red-700 text-red-800 dark:text-red-200 rounded-xl shadow-lg backdrop-blur-sm animate-slide-down">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle mr-3 text-lg"></i>
                        <div>{{ form.non_field_errors }}</div>
                    </div>
                </div>
            {% endif %}

            <!-- Project Info -->
            <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 mb-8">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">معلومات المشروع</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">اسم المشروع</label>
                        <p class="text-gray-900 dark:text-white font-semibold">{{ project.name }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">حالة المشروع</label>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                            {% if project.status == 'completed' %}bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                            {% elif project.status == 'in_progress' %}bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                            {% elif project.status == 'planning' %}bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                            {% elif project.status == 'on_hold' %}bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200
                            {% else %}bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200{% endif %}">
                            {{ project.get_status_display }}
                        </span>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <!-- Employee Selection -->
                <div class="md:col-span-2">
                    <label for="{{ form.employee.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <i class="fas fa-user ml-2 text-primary-600 dark:text-primary-400"></i>{{ form.employee.label }}
                    </label>
                    {{ form.employee }}
                    {% if form.employee.errors %}
                        <div class="mt-2 text-red-600 dark:text-red-400 text-sm">
                            {% for error in form.employee.errors %}
                                <p><i class="fas fa-exclamation-circle ml-1"></i>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                    {% if project_employee %}
                        <div class="mt-3 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                            <p class="text-sm text-blue-800 dark:text-blue-200">
                                <i class="fas fa-info-circle ml-2"></i>
                                الموظف الحالي: {{ project_employee.employee.full_name }} - {{ project_employee.employee.position }}
                            </p>
                        </div>
                    {% endif %}
                </div>

                <!-- Role in Project -->
                <div class="md:col-span-2">
                    <label for="{{ form.role.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <i class="fas fa-briefcase ml-2 text-primary-600 dark:text-primary-400"></i>{{ form.role.label }}
                    </label>
                    {{ form.role }}
                    {% if form.role.errors %}
                        <div class="mt-2 text-red-600 dark:text-red-400 text-sm">
                            {% for error in form.role.errors %}
                                <p><i class="fas fa-exclamation-circle ml-1"></i>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                        <i class="fas fa-lightbulb ml-1"></i>
                        مثال: مطور، مصمم، مدير فريق، محلل أعمال، مختبر...
                    </p>
                </div>

                <!-- Assignment Date -->
                <div class="md:col-span-2">
                    <label for="{{ form.assigned_date.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <i class="fas fa-calendar-plus ml-2 text-primary-600 dark:text-primary-400"></i>{{ form.assigned_date.label }}
                    </label>
                    {{ form.assigned_date }}
                    {% if form.assigned_date.errors %}
                        <div class="mt-2 text-red-600 dark:text-red-400 text-sm">
                            {% for error in form.assigned_date.errors %}
                                <p><i class="fas fa-exclamation-circle ml-1"></i>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Available Employees Info -->
            {% if not project_employee %}
                <div class="mt-8 p-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl">
                    <h4 class="text-lg font-semibold text-green-800 dark:text-green-200 mb-3">
                        <i class="fas fa-info-circle ml-2"></i>معلومات مهمة
                    </h4>
                    <ul class="text-sm text-green-700 dark:text-green-300 space-y-2">
                        <li><i class="fas fa-check ml-2"></i>يتم عرض الموظفين النشطين فقط</li>
                        <li><i class="fas fa-check ml-2"></i>الموظفون المُعيَّنون مسبقاً لهذا المشروع لن يظهروا في القائمة</li>
                        <li><i class="fas fa-check ml-2"></i>يمكن تحديد دور الموظف في المشروع (اختياري)</li>
                    </ul>
                </div>
            {% endif %}

            <div class="mt-12 flex justify-start space-x-4 space-x-reverse">
                <button type="submit" class="bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 btn-glow">
                    <i class="fas fa-save ml-2"></i>{{ submit_text }}
                </button>
                <a href="{% url 'employees:project_detail' project.pk %}" class="bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
                    <i class="fas fa-times ml-2"></i>إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<script>
// Show employee details when selected
document.getElementById('{{ form.employee.id_for_label }}').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    if (selectedOption.value) {
        // You can add AJAX call here to fetch and display employee details
        console.log('Selected employee:', selectedOption.text);
    }
});
</script>
{% endblock %}
