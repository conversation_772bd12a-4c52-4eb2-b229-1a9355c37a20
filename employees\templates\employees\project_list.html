{% extends 'base.html' %}

{% block title %}قائمة المشاريع - Employee Management System{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Header Section -->
    <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg shadow-2xl rounded-2xl border border-gray-200/50 dark:border-gray-700/50 hover-lift mb-8">
        <div class="px-8 py-6 border-b border-gray-200/50 dark:border-gray-700/50 bg-gradient-to-r from-primary-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 rounded-t-2xl">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <i class="fas fa-project-diagram text-3xl text-primary-600 dark:text-primary-400 float"></i>
                        <div class="absolute -inset-2 bg-primary-600/20 rounded-full blur opacity-50"></div>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold bg-gradient-to-r from-primary-600 to-primary-800 dark:from-primary-400 dark:to-primary-600 bg-clip-text text-transparent">
                            قائمة المشاريع
                        </h1>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">إدارة مشاريع الشركة</p>
                    </div>
                </div>
                <a href="{% url 'employees:project_create' %}" class="bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 btn-glow">
                    <i class="fas fa-plus ml-2"></i>مشروع جديد
                </a>
            </div>
        </div>

        <!-- Search and Filter Section -->
        <div class="p-6">
            <form method="get" class="flex flex-col md:flex-row gap-4">
                <div class="flex-1">
                    <input type="text" name="search" value="{{ search_query }}" 
                           placeholder="البحث في المشاريع..." 
                           class="w-full rounded-xl border-0 bg-gray-50 dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 px-4 py-3">
                </div>
                <div class="md:w-48">
                    <select name="status" class="w-full rounded-xl border-0 bg-gray-50 dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white px-4 py-3">
                        <option value="">جميع الحالات</option>
                        {% for value, label in project_status_choices %}
                            <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                <button type="submit" class="bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
                    <i class="fas fa-search ml-2"></i>بحث
                </button>
            </form>
        </div>
    </div>

    <!-- Projects Grid -->
    {% if page_obj %}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {% for project in page_obj %}
                <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg shadow-2xl rounded-2xl border border-gray-200/50 dark:border-gray-700/50 hover-lift transition-all duration-300 overflow-hidden">
                    <!-- Project Image -->
                    {% if project.image %}
                        <div class="h-48 bg-cover bg-center" style="background-image: url('{{ project.image.url }}');">
                            <div class="h-full bg-gradient-to-t from-black/50 to-transparent flex items-end p-4">
                                <span class="text-white text-sm font-medium">{{ project.get_status_display }}</span>
                            </div>
                        </div>
                    {% else %}
                        <div class="h-48 bg-gradient-to-br from-primary-500 to-primary-700 flex items-center justify-center">
                            <i class="fas fa-project-diagram text-6xl text-white/50"></i>
                        </div>
                    {% endif %}

                    <!-- Project Content -->
                    <div class="p-6">
                        <div class="flex items-start justify-between mb-3">
                            <h3 class="text-xl font-bold text-gray-900 dark:text-white">{{ project.name }}</h3>
                            <span class="px-3 py-1 text-xs font-medium rounded-full
                                {% if project.status == 'completed' %}bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                {% elif project.status == 'in_progress' %}bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                {% elif project.status == 'planning' %}bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                                {% elif project.status == 'on_hold' %}bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200
                                {% else %}bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200{% endif %}">
                                {{ project.get_status_display }}
                            </span>
                        </div>

                        <p class="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-3">
                            {{ project.description|truncatewords:20 }}
                        </p>

                        <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
                            <div class="flex items-center">
                                <i class="fas fa-users ml-2"></i>
                                <span>{{ project.employees_count }} موظف</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-calendar ml-2"></i>
                                <span>{{ project.created_at|date:"Y/m/d" }}</span>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex gap-2">
                            <a href="{% url 'employees:project_detail' project.pk %}" 
                               class="flex-1 bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white text-center px-4 py-2 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 text-sm">
                                <i class="fas fa-eye ml-1"></i>عرض
                            </a>
                            <a href="{% url 'employees:project_update' project.pk %}" 
                               class="flex-1 bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-700 hover:to-yellow-800 text-white text-center px-4 py-2 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 text-sm">
                                <i class="fas fa-edit ml-1"></i>تحديث
                            </a>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
            <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg shadow-2xl rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-6">
                <nav class="flex items-center justify-between">
                    <div class="flex items-center text-sm text-gray-700 dark:text-gray-300">
                        <span>عرض {{ page_obj.start_index }} إلى {{ page_obj.end_index }} من {{ page_obj.paginator.count }} مشروع</span>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        {% if page_obj.has_previous %}
                            <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" 
                               class="px-4 py-2 text-sm font-medium text-gray-500 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200">
                                السابق
                            </a>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <span class="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-primary-600 rounded-lg">{{ num }}</span>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <a href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" 
                                   class="px-4 py-2 text-sm font-medium text-gray-500 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200">{{ num }}</a>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" 
                               class="px-4 py-2 text-sm font-medium text-gray-500 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200">
                                التالي
                            </a>
                        {% endif %}
                    </div>
                </nav>
            </div>
        {% endif %}
    {% else %}
        <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg shadow-2xl rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-12 text-center">
            <div class="relative mb-6">
                <i class="fas fa-project-diagram text-6xl text-gray-400 dark:text-gray-600 float"></i>
                <div class="absolute -inset-4 bg-gray-400/20 rounded-full blur opacity-50"></div>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">لا توجد مشاريع</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-6">لم يتم العثور على أي مشاريع. ابدأ بإنشاء مشروع جديد.</p>
            <a href="{% url 'employees:project_create' %}" class="bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 btn-glow">
                <i class="fas fa-plus ml-2"></i>إنشاء مشروع جديد
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}
